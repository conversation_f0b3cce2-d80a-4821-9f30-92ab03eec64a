
<div style="width:100%; height: 100%; position: fixed; top: 0; left: 0; z-index: 9999; display: none;" id="call-interface-container">
<app-incomming-call></app-incoming-call>
</div>

<div class="container-fluid" *ngIf="showHeader()">
  <div class="row" >
    <div class="col-1 p-0">
      <app-sidebar></app-sidebar>
    </div>
    <div class="col-11">
      <div class="row">
      <div class="col-12 p-0">
  <app-header></app-header>
      </div>
      </div>
      
        
        <!-- <app-notification></app-notification> -->
        <router-outlet></router-outlet>
        <app-my-loader></app-my-loader>
      
    </div>
  </div>
  
</div>
<div class="container-fluid px-0 land_page" *ngIf="!showHeader()">
    <router-outlet name='baseRouter'></router-outlet>
</div>
<!-- <app-footer></app-footer> -->